import { DynamoDB } from 'aws-sdk';
import { User, CreateUserInput, ChildAccountResponse, ChildAccountsResponse, OperationCounts } from '../types';
import { dynamoDBClient } from '../database';
import { generatePublicKey } from '../utils/apiKeyUtils';
import { VERIFICATION_TOKEN_EXPIRY } from '../constants';
import { validateOrganizationName, validateOrganizationUrl } from '../validation/organizationValidation';
import { AuthUrls } from '../types';
import * as crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

// Use the same DynamoDB client instance that SignUpLambda uses
const dynamoDB = new DynamoDB.DocumentClient({
  region: process.env.AWS_REGION || 'eu-west-1'
});


const TABLE_NAME = process.env.USER_DETAILS_TABLE_NAME || 'userAuthentication';

export async function getUserByEmail(email: string, tableName?: string): Promise<User | null> {
  const params = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'emailIndex',
    KeyConditionExpression: 'email = :email',
    ExpressionAttributeValues: { ':email': email },
    ProjectionExpression: [
      'userID',
      'email',
      'username',
      'parentAccount',
      'emailVerified',
      'password',
      'accountStatus',
      'accountType',
      'jwtSecret',
      'publicKey',
      'resetPasswordOTP',
      'resetPasswordOTPExpiry',
      'lastResetPasswordRequestAt',
      'lastPasswordChanged',
      'createdAt',
      'organizationName',          // Added
      'organizationUrl',           // Added
      'isPromotedAccount',         // NEW: Organization ID approach
      'organizationId',            // NEW: Organization ID approach
      'promotedAt'                 // NEW: Organization ID approach
    ].join(', ')
  };

  const result = await dynamoDB.query(params).promise();
  return result.Items?.[0] as User || null;
}

export async function getUserByUsername(username: string, tableName?: string): Promise<User | null> {
  const params = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'usernameIndex', 
    KeyConditionExpression: 'username = :username',
    ExpressionAttributeValues: { ':username': username },
    ProjectionExpression: 'userID, username' // Only fetch needed fields
  };

  const result = await dynamoDB.query(params).promise();
  return result.Items?.[0] as User || null;
}

export async function createUser(input: CreateUserInput, tableName?: string): Promise<User> {
  const userID = uuidv4();
  const publicKey = generatePublicKey();
  const now = Date.now();
  
  // Set accountType based on the value of parentAccount
  const accountType = input.parentAccount === 'ROOT' ? 'parent' : 'child';
  
  // Generate JWT secret for parent accounts only
  const jwtSecret = accountType === 'parent' ? crypto.randomBytes(32).toString('hex') : undefined;

  const params = {
    TableName: tableName || TABLE_NAME,
    Item: {
      userID,
      username: input.username,
      email: input.email,
      password: input.password,
      emailVerified: false,
      accountStatus: 'active',
      createdAt: now,
      publicKey: publicKey, // Updated field name
      verificationToken: input.verificationToken,
      verificationTokenExpiry: input.verificationTokenExpiry,
      parentAccount: input.parentAccount || 'ROOT',
      organizationName: input.organizationName || null,
      organizationUrl: input.organizationUrl || null,
      accountType: accountType,
      accountBalance: input.accountBalance,
      availableBalance: input.availableBalance,
      jwtSecret: jwtSecret
    }
  };
  
  try {
    await dynamoDB.put(params).promise();
    return params.Item as User;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
}

export const updateUserVerificationToken = async (userID: string, newToken: string, tableName?: string) => {
  console.log('Updating verification token:', {
    userID,
    newToken,
    type: typeof newToken
  });

  if (!/^[0-9A-Z]{12}$/.test(newToken)) {
    throw new Error('Invalid verification token format');
  }

  return await dynamoDB.update({
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET verificationToken = :token, verificationTokenExpiry = :expiry, lastVerificationSentAt = :sent',
    ExpressionAttributeValues: {
      ':token': newToken.toString(),
      ':expiry': Date.now() + VERIFICATION_TOKEN_EXPIRY,
      ':sent': Date.now()
    }
  }).promise();
};

export const clearUserVerificationToken = async (userID: string, tableName?: string) => {
  return await dynamoDB.update({
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET emailVerified = :verified, verificationToken = :token, verificationTokenExpiry = :expiry',
    ExpressionAttributeValues: {
      ':verified': true,
      ':token': null,
      ':expiry': 0
    }
  }).promise();
};

export async function getUsersByAccountStatus(status: string, fromDate?: number, toDate?: number, tableName?: string): Promise<User[]> {
  const params: any = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'accountStatusIndex',
    KeyConditionExpression: 'accountStatus = :status'
  };

  if (fromDate && toDate) {
    params.KeyConditionExpression += ' AND createdAt BETWEEN :fromDate AND :toDate';
    params.ExpressionAttributeValues = {
      ':status': status,
      ':fromDate': fromDate,
      ':toDate': toDate
    };
  } else {
    params.ExpressionAttributeValues = {
      ':status': status
    };
  }

  const result = await dynamoDB.query(params).promise();
  return result.Items as User[];
}

export async function getUserByPublicKey(publicKey: string, tableName?: string): Promise<User | null> {
  // Extra logging and guard clause for debugging
  if (!publicKey || typeof publicKey !== 'string' || publicKey.trim() === '') {
    throw new Error('publicKey must be a non-empty string');
  }

  const params = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'publicKeyIndex', // Updated index name
    KeyConditionExpression: 'publicKey = :publicKey',
    ExpressionAttributeValues: {
      ':publicKey': publicKey
    }
  };

  const result = await dynamoDB.query(params).promise();
  return result.Items?.[0] as User || null;
}

export const updateUserResetPasswordOTP = async (
  userID: string, 
  resetPasswordData: {
    resetPasswordOTP: string;
    resetPasswordOTPExpiry: number;
    lastResetPasswordRequestAt: number;
  },
  tableName?: string
) => {
  return await dynamoDB.update({
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET resetPasswordOTP = :otp, resetPasswordOTPExpiry = :expiry, lastResetPasswordRequestAt = :sent',
    ExpressionAttributeValues: {
      ':otp': resetPasswordData.resetPasswordOTP,
      ':expiry': resetPasswordData.resetPasswordOTPExpiry,
      ':sent': resetPasswordData.lastResetPasswordRequestAt
    }
  }).promise();
};

export const updateUserPassword = async (userID: string, hashedPassword: string, timestamp: number = Date.now(), tableName?: string): Promise<void> => {
  console.log('Updating password - Input:', {
    userID,
    hashedPasswordLength: hashedPassword?.length,
    tableName: tableName || TABLE_NAME
  });

  if (!hashedPassword) {
    console.error('hashedPassword is undefined or null');
    throw new Error('hashedPassword must be provided');
  }

  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET password = :password, resetPasswordOTP = :resetOTP, resetPasswordOTPExpiry = :resetOTPExpiry, lastPasswordChanged = :lastPasswordChanged',
    ExpressionAttributeValues: {
      ':password': hashedPassword,
      ':resetOTP': '',
      ':resetOTPExpiry': 0,
      ':lastPasswordChanged': timestamp
    }
  };

  await dynamoDB.update(params).promise();
};

export const getUserByVerificationToken = async (token: string, tableName?: string): Promise<User | null> => {
  const params = {
    TableName: tableName || TABLE_NAME,
    FilterExpression: 'verificationToken = :token',
    ExpressionAttributeValues: {
      ':token': token
    }
  };

  const result = await dynamoDB.scan(params).promise();
  return (result.Items?.[0] as User) || null;
};

export const confirmUserEmail = async (userID: string, tableName?: string): Promise<void> => {
  await dynamoDB.update({
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET emailVerified = :verified, verificationToken = :token, verificationTokenExpiry = :expiry',
    ExpressionAttributeValues: {
      ':verified': true,
      ':token': null,
      ':expiry': 0
    }
  }).promise();
};

export async function checkDuplicateEmail(parentPublicKey: string, email: string): Promise<boolean> {
  if (!email) {
    throw new Error('Email is required for duplicate check');
  }
  
  const params = {
    TableName: TABLE_NAME,
    IndexName: 'parentAccountEmailIndex',
    KeyConditionExpression: 'parentAccount = :parentAccount AND email = :email',
    ExpressionAttributeValues: {
      ':parentAccount': parentPublicKey,
      ':email': email
    }
  };

  try {
    const result = await dynamoDB.query(params).promise();
    return Boolean(result.Items && result.Items.length > 0);
  } catch (error) {
    console.error('Error checking for duplicate email:', error);
    throw error;
  }
}

export const updateOrganization = async (
  userID: string,
  organizationName?: string | undefined,
  organizationUrl?: string | undefined,
  authUrls?: AuthUrls | undefined,
  domainRestrictionEnabled?: boolean | undefined,
  tableName?: string,
  googleSsoConfig?: { enabled: boolean; clientId: string; clientSecret: string } | undefined
): Promise<void> => {
  // Build update expression and attribute values dynamically
  let updateExpressionParts: string[] = [];
  const expressionAttributeValues: any = {
    ':zero': 0,
    ':one': 1
  };

  if (organizationName !== undefined) {
    updateExpressionParts.push('organizationName = :name');
    expressionAttributeValues[':name'] = organizationName;
  }

  if (organizationUrl !== undefined) {
    updateExpressionParts.push('organizationUrl = :url');
    expressionAttributeValues[':url'] = organizationUrl;
  }

  if (authUrls !== undefined) {
    updateExpressionParts.push('authUrls = :authUrls');
    expressionAttributeValues[':authUrls'] = authUrls;
  }

  if (domainRestrictionEnabled !== undefined) {
    updateExpressionParts.push('domainRestrictionEnabled = :domainRestriction');
    expressionAttributeValues[':domainRestriction'] = domainRestrictionEnabled;
    console.log(`Setting domainRestrictionEnabled to ${domainRestrictionEnabled} for user ${userID}`);
  }

  if (googleSsoConfig !== undefined) {
    updateExpressionParts.push('googleSsoConfig = :googleSsoConfig');
    expressionAttributeValues[':googleSsoConfig'] = googleSsoConfig;
  }

  // Always increment the counter
  updateExpressionParts.push('organizationUpdateCount = if_not_exists(organizationUpdateCount, :zero) + :one');

  const updateExpression = `SET ${updateExpressionParts.join(', ')}`;

  await dynamoDB.update({
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: updateExpression,
    ExpressionAttributeValues: expressionAttributeValues
  }).promise();
};

export async function getUserByEmailAndParent(email: string, parentPublicKey: string, tableName?: string): Promise<User | null> {
  const params = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'emailIndex',
    KeyConditionExpression: 'email = :email',
    FilterExpression: 'parentAccount = :parentAccount',
    ExpressionAttributeValues: { 
      ':email': email,
      ':parentAccount': parentPublicKey
    }
  };

  const result = await dynamoDB.query(params).promise();
  return result.Items?.[0] as User || null;
}

export const getChildAccountsByParent = async (
    parentPublicKey: string,
    searchTerm?: string,
    startDate?: number,
    endDate?: number,
    limit: number = 50,
    lastEvaluatedKey?: any,
    tableName?: string
): Promise<ChildAccountsResponse> => {
    console.log('getChildAccountsByParent called with:', {
        parentPublicKey,
        searchTerm,
        startDate: startDate ? new Date(startDate).toISOString() : null,
        endDate: endDate ? new Date(endDate).toISOString() : null,
        limit,
        hasLastKey: !!lastEvaluatedKey
    });

    // Base query parameters that will be used for both count and data queries
    const baseParams = {
        TableName: tableName || TABLE_NAME,  // Use passed tableName with fallback to default
        IndexName: 'parentAccountIndex',
        KeyConditionExpression: startDate && endDate 
            ? 'parentAccount = :parentPublicKey AND createdAt BETWEEN :startDate AND :endDate'
            : 'parentAccount = :parentPublicKey',
        ExpressionAttributeValues: {
            ':parentPublicKey': parentPublicKey,
            ...(startDate && endDate && {
                ':startDate': startDate,
                ':endDate': endDate
            })
        }
    };

    console.log('Base query parameters:', {
        tableName: baseParams.TableName,
        indexName: baseParams.IndexName,
        keyCondition: baseParams.KeyConditionExpression,
        expressionValues: baseParams.ExpressionAttributeValues
    });

    // Get total counts with the same filters
    const countParams: DynamoDB.DocumentClient.QueryInput = {
        ...baseParams,
        Select: 'COUNT'
    };

    // Get paginated results
    const queryParams: DynamoDB.DocumentClient.QueryInput = {
        ...baseParams,
        Limit: limit,
        ExclusiveStartKey: lastEvaluatedKey
    };

    if (searchTerm) {
        queryParams.FilterExpression = 'contains(username, :searchTerm) OR contains(email, :searchTerm)';
        queryParams.ExpressionAttributeValues = {
            ...queryParams.ExpressionAttributeValues,
            ':searchTerm': searchTerm.toLowerCase()
        };
        console.log('Added search parameters:', {
            filterExpression: queryParams.FilterExpression,
            searchTerm: searchTerm.toLowerCase()
        });
    }

    try {
        // Get total count first
        console.log('Executing count query with params:', countParams);
        const countResult = await dynamoDB.query(countParams).promise();
        console.log('Count query result:', countResult);
        const totalAccounts = countResult.Count || 0;

        // Then get paginated results
        console.log('Executing main query with params:', queryParams);
        const result = await dynamoDB.query(queryParams).promise();
        console.log('Main query result:', {
            itemCount: result.Items?.length || 0,
            hasLastKey: !!result.LastEvaluatedKey,
            scannedCount: result.ScannedCount,
            consumedCapacity: result.ConsumedCapacity
        });

        const accounts = (result.Items || []).map((item: any) => ({
            username: item.username,
            email: item.email,
            lastSeen: item.lastLoginAttempt ? new Date(item.lastLoginAttempt).toISOString() : null,
            registered: new Date(item.createdAt).toISOString()
        }));

        // Calculate child-only operation counts (removing parent-only operations)
        const childOperationCounts = (result.Items || []).reduce((counts: OperationCounts, item: any) => ({
            emailConfirmation: (counts.emailConfirmation || 0) + (item.emailConfirmationCount || 0),
            resendConfirmation: (counts.resendConfirmation || 0) + (item.resendEmailCount || 0),
            resetPassword: (counts.resetPassword || 0) + (item.resetPasswordRequestCount || 0),
            updatePassword: (counts.updatePassword || 0) + (item.passwordUpdateCount || 0),
            signIn: (counts.signIn || 0) + (item.signInCount || 0),
            organizationUpdate: 0,  // Child accounts can't update organization
            organizationDetailsRetrieval: 0,  // Child accounts can't retrieve org details
            childAccountsListRetrieval: 0,  // Child accounts can't list child accounts
            childAccounts: totalAccounts
        }), {
            emailConfirmation: 0,
            resendConfirmation: 0,
            resetPassword: 0,
            updatePassword: 0,
            signIn: 0,
            organizationUpdate: 0,
            organizationDetailsRetrieval: 0,
            childAccountsListRetrieval: 0,
            childAccounts: 0
        });

        // Calculate active accounts using the same base parameters
        const activeQueryParams = {
            ...baseParams,
            FilterExpression: 'lastLoginAttempt >= :thirtyDaysAgo',
            ExpressionAttributeValues: {
                ...baseParams.ExpressionAttributeValues,
                ':thirtyDaysAgo': Date.now() - (30 * 24 * 60 * 60 * 1000)
            }
        };
        
        console.log('Executing active accounts query with params:', activeQueryParams);
        const activeResult = await dynamoDB.query(activeQueryParams).promise();
        const activeAccounts = activeResult.Count || 0;
        
        console.log('Active accounts query result:', {
            activeCount: activeAccounts,
            scannedCount: activeResult.ScannedCount
        });

        const response = {
            accounts,
            lastEvaluatedKey: result.LastEvaluatedKey,
            totalAccounts: totalAccounts,  // Just the child accounts count
            activeAccounts,
            operationCounts: childOperationCounts  // Only child operations, no combining with parent
        };
        console.log('Final response:', {
            accountCount: accounts.length,
            hasLastKey: !!result.LastEvaluatedKey,
            totalAccounts,
            activeAccounts
        });

        return response;
    } catch (error) {
        console.error('Error in getChildAccountsByParent:', error);
        throw error;
    }
};

// Add these helper functions
export const incrementEmailConfirmationCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    const params = {
      TableName: tableName || TABLE_NAME,
      Key: { userID },
      UpdateExpression: 'SET emailConfirmationCount = if_not_exists(emailConfirmationCount, :zero) + :one',
      ExpressionAttributeValues: {
        ':zero': 0,
        ':one': 1
      },
      ReturnValues: 'UPDATED_NEW'
    };
    
    const result = await dynamoDB.update(params).promise();
    console.log(`Successfully updated emailConfirmationCount for user ${userID}:`, result.Attributes);
  } catch (error) {
    console.error('Error incrementing emailConfirmationCount:', error);
    throw error;
  }
};

export const incrementResendEmailCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'resendEmailCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing resendEmailCount:', error);
    throw error;
  }
};

export const incrementResetPasswordRequestCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'resetPasswordRequestCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing resetPasswordRequestCount:', error);
    throw error;
  }
};

export const incrementPasswordUpdateCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'passwordUpdateCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing passwordUpdateCount:', error);
    throw error;
  }
};

export const incrementSignInCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'signInCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing signInCount:', error);
    throw error;
  }
};

export const incrementOrganizationDetailsRetrievalCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'organizationDetailsRetrievalCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing organizationDetailsRetrievalCount:', error);
    throw error;
  }
};

export const incrementChildAccountsListRetrievalCount = async (userID: string, tableName?: string): Promise<void> => {
  try {
    
    return await updateUserAttribute(userID, 'childAccountsListRetrievalCount', 1, true, tableName);
  } catch (error) {
    console.error('Error incrementing childAccountsListRetrievalCount:', error);
    throw error;
  }
};

// Helper function for atomic increments
const updateUserAttribute = async (
  userID: string, 
  attributeName: string, 
  increment: number,
  createIfNotExists: boolean = true,
  tableName?: string
): Promise<any> => {
  try {
    const params = {
      TableName: tableName || TABLE_NAME,
      Key: { userID },
      UpdateExpression: `SET #attr = if_not_exists(#attr, :zero) + :inc`,
      ExpressionAttributeNames: {
        '#attr': attributeName
      },
      ExpressionAttributeValues: {
        ':inc': increment,
        ':zero': 0
      },
      ReturnValues: 'UPDATED_NEW'
    };

    const result = await dynamoDB.update(params).promise();
    console.log(`Successfully updated ${attributeName} for user ${userID}:`, result.Attributes);
    return result.Attributes;
  } catch (error) {
    console.error(`Error updating ${attributeName} for user ${userID}:`, error);
    // Return null instead of throwing error to prevent signup flow interruption
    return null;
  }
};

export const getParentAccountOperations = async (parentPublicKey: string, tableName?: string): Promise<OperationCounts> => {
    try {
        // Get parent account
        const parentAccount = await getUserByPublicKey(parentPublicKey, tableName);
        if (!parentAccount) {
            throw new Error(`Parent account not found for public key: ${parentPublicKey}`);
        }

        // Return operation counts
        return {
            emailConfirmation: parentAccount.emailConfirmationCount || 0,
            resendConfirmation: parentAccount.resendEmailCount || 0,
            resetPassword: parentAccount.resetPasswordRequestCount || 0,
            updatePassword: parentAccount.passwordUpdateCount || 0,
            signIn: parentAccount.signInCount || 0,
            organizationUpdate: parentAccount.organizationUpdateCount || 0,
            organizationDetailsRetrieval: parentAccount.organizationDetailsRetrievalCount || 0,
            childAccountsListRetrieval: parentAccount.childAccountsListRetrievalCount || 0,
            childAccounts: 0  // This will be added later
        };
    } catch (error) {
        console.error('Error fetching parent account operations:', error);
        throw error;
    }
};

export const checkAndResetMonthlyOperations = async (userID: string, tableName?: string): Promise<void> => {
  try {
    // Reset all operation counts without checking the month
    const updateParams = {
      TableName: tableName || TABLE_NAME,
      Key: { userID },
      UpdateExpression: `
        SET emailConfirmationCount = :zero,
            resendEmailCount = :zero,
            resetPasswordRequestCount = :zero,
            passwordUpdateCount = :zero,
            signInCount = :zero,
            organizationUpdateCount = :zero,
            organizationDetailsRetrievalCount = :zero,
            childAccountsListRetrievalCount = :zero,
            lastResetDate = :now
      `,
      ExpressionAttributeValues: {
        ':zero': 0,
        ':now': Date.now()
      }
    };
    
    await dynamoDB.update(updateParams).promise();
    console.log(`Reset monthly operations for user ${userID}`);
  } catch (error) {
    console.error('Error in checkAndResetMonthlyOperations:', error);
    throw error;
  }
};

export const resetAllChildAccountCounters = async (tableName?: string): Promise<void> => {
    try {
        // Query all child accounts
        const params = {
            TableName: tableName || TABLE_NAME,
            FilterExpression: 'accountType = :accountType',
            ExpressionAttributeValues: {
                ':accountType': 'child'
            }
        };

        const result = await dynamoDB.scan(params).promise();
        const childAccounts = result.Items || [];
        
        console.log(`Found ${childAccounts.length} child accounts to reset`);

        // Reset counters for each child account
        for (const account of childAccounts) {
            const updateParams = {
                TableName: tableName || TABLE_NAME,
                Key: { userID: account.userID },
                UpdateExpression: `
                    SET emailConfirmationCount = :zero,
                        resendEmailCount = :zero,
                        resetPasswordRequestCount = :zero,
                        passwordUpdateCount = :zero,
                        signInCount = :zero,
                        lastResetDate = :now
                `,
                ExpressionAttributeValues: {
                    ':zero': 0,
                    ':now': Date.now()
                }
            };
            
            await dynamoDB.update(updateParams).promise();
        }
        
        console.log(`Successfully reset counters for ${childAccounts.length} child accounts`);
    } catch (error) {
        console.error('Error resetting child account counters:', error);
        throw error;
    }
};

export async function updateUserLastBalanceNotification(
  userID: string, 
  notificationType: 'warning' | 'critical' | 'depleted',
  timestamp: number,
  tableName?: string
): Promise<void> {
  const updateField = 
    notificationType === 'warning' ? 'lastLowBalanceNotificationAt' :
    notificationType === 'critical' ? 'lastCriticalBalanceNotificationAt' : 
    'lastDepletedBalanceNotificationAt';
  
  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: `set ${updateField} = :timestamp`,
    ExpressionAttributeValues: {
      ':timestamp': timestamp
    }
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`Updated ${updateField} for user ${userID} to ${timestamp}`);
  } catch (error) {
    console.error(`Error updating ${updateField} for user ${userID}:`, error);
    throw error;
  }
}

export async function getUserById(userID: string, tableName?: string): Promise<User | null> {
  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID }
  };

  try {
    const result = await dynamoDB.get(params).promise();
    return result.Item as User || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
}

export async function updateUserBalance(userID: string, newBalance: number, tableName?: string): Promise<void> {
  console.log(`Updating balance for user ${userID} to ${newBalance}`);

  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET availableBalance = :newBalance',
    ConditionExpression: ':newBalance >= :zero',
    ExpressionAttributeValues: {
      ':newBalance': newBalance,
      ':zero': 0
    }
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`Successfully updated balance for user ${userID} to ${newBalance}`);
  } catch (error: any) {
    if (error?.code === 'ConditionalCheckFailedException') {
      throw new Error('Cannot set negative balance');
    }
    console.error('Error updating user balance:', error);
    throw error;
  }
}

/**
 * Promotes a child account to a parent account
 * - Changes accountType to 'parent'
 * - Changes parentAccount to 'ROOT'
 * - Generates and sets a JWT secret
 * - Sets initial account balance
 * - Stamps with organization ID for widget authentication
 */
export async function promoteChildToParent(userID: string, tableName?: string): Promise<void> {
  console.log(`[PROMOTE] Starting promotion process for user ${userID}`);

  // Step 1: Get current user to find their parent account
  const currentUser = await getUserById(userID, tableName);
  if (!currentUser) {
    console.error(`[PROMOTE] User ${userID} not found`);
    throw new Error('User not found');
  }

  console.log(`[PROMOTE] Found user ${userID}, accountType: ${currentUser.accountType}, parentAccount: ${currentUser.parentAccount}`);

  // Step 2: Get parent account details to derive organization
  const parentUser = await getUserByPublicKey(currentUser.parentAccount, tableName);
  if (!parentUser) {
    console.error(`[PROMOTE] Parent account ${currentUser.parentAccount} not found for user ${userID}`);
    throw new Error('Parent account not found');
  }

  const organizationId = parentUser.organizationName;
  if (!organizationId) {
    console.error(`[PROMOTE] Parent account ${currentUser.parentAccount} has no organization name`);
    throw new Error('Parent account has no organization');
  }

  console.log(`[PROMOTE] Parent account ${currentUser.parentAccount} belongs to organization: ${organizationId}`);

  // Step 3: Check if this organization is allowed to promote accounts
  const { isSpecialOrganization } = await import('../config/organizationConfig');
  if (!isSpecialOrganization(organizationId)) {
    console.error(`[PROMOTE] Organization "${organizationId}" not authorized for account promotion`);
    throw new Error('Organization not authorized for account promotion');
  }

  console.log(`[PROMOTE] Organization "${organizationId}" is authorized for promotion`);

  // Step 4: Generate promotion credentials
  const jwtSecret = crypto.randomBytes(32).toString('hex');
  const initialBalance = 3.00;
  const promotionTimestamp = Date.now();

  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: `
      SET accountType = :parentType,
          parentAccount = :rootParent,
          jwtSecret = :jwtSecret,
          accountBalance = :balance,
          availableBalance = :balance,
          isPromotedAccount = :promoted,
          organizationId = :orgId,
          promotedAt = :timestamp
    `,
    ConditionExpression: 'accountType = :childType',
    ExpressionAttributeValues: {
      ':parentType': 'parent',
      ':rootParent': 'ROOT',
      ':jwtSecret': jwtSecret,
      ':balance': initialBalance,
      ':promoted': true,
      ':orgId': organizationId,
      ':timestamp': promotionTimestamp,
      ':childType': 'child'
    },
    ReturnValues: 'NONE'
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`[PROMOTE] Successfully promoted user ${userID} to parent account`);
    console.log(`[PROMOTE] Details - Balance: ${initialBalance}, Organization: ${organizationId}, Timestamp: ${promotionTimestamp}`);
  } catch (error: any) {
    if (error.code === 'ConditionalCheckFailedException') {
      console.error(`[PROMOTE] Conditional check failed for user ${userID} - not a child account or doesn't exist`);
      throw new Error('Account is not a child account or does not exist');
    }
    console.error(`[PROMOTE] Error promoting user ${userID}:`, error);
    throw error;
  }
}

export const getUserByGoogleId = async (googleId: string, parentAccount: string, tableName?: string): Promise<User | null> => {
  const params = {
    TableName: tableName || TABLE_NAME,
    IndexName: 'googleId-parentAccount-index',
    KeyConditionExpression: 'googleId = :googleId and parentAccount = :parentAccount',
    ExpressionAttributeValues: {
      ':googleId': googleId,
      ':parentAccount': parentAccount,
    },
  };
  const { Items } = await dynamoDB.query(params).promise();
  return Items && Items.length > 0 ? (Items[0] as User) : null;
};

export const linkGoogleAccount = async (userID: string, googleId: string, tableName?: string): Promise<User> => {
  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: 'SET googleId = :googleId, authProvider = :authProvider, lastLoginProvider = :lastLoginProvider',
    ExpressionAttributeValues: {
      ':googleId': googleId,
      ':authProvider': 'google',
      ':lastLoginProvider': 'google'
    },
    ReturnValues: 'ALL_NEW',
  };

  const result = await dynamoDB.update(params).promise();
  if (!result.Attributes) {
    throw new Error(`User with ID ${userID} not found.`);
  }
  return result.Attributes as User;
};

export const createUserFromGoogle = async (
  googleProfile: { sub: string; email: string; name: string },
  parentAccount: string,
  tableName?: string
): Promise<User> => {
  const userID = uuidv4();
  const newUser: User = {
    userID,
    email: googleProfile.email,
    username: googleProfile.name,
    accountType: 'child',
    parentAccount,
    accountStatus: 'active',
    emailVerified: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    publicKey: generatePublicKey(),
    secretKey: '', 
    iv: '', 
    authProvider: 'google',
    googleId: googleProfile.sub,
    lastLogin: Date.now(),
    lastLoginProvider: 'google',
  };

  const params = {
    TableName: tableName || TABLE_NAME,
    Item: newUser,
  };

  await dynamoDB.put(params).promise();
  return newUser;
};

export const updateUser = async (
  userID: string,
  updateData: Partial<User>,
  tableName?: string
): Promise<User> => {
  const updateExpressionParts: string[] = [];
  const expressionAttributeValues: { [key: string]: any } = {};

  for (const [key, value] of Object.entries(updateData)) {
    if (value !== undefined) {
      updateExpressionParts.push(`${key} = :${key}`);
      expressionAttributeValues[`:${key}`] = value;
    }
  }
  
  const params = {
    TableName: tableName || TABLE_NAME,
    Key: { userID },
    UpdateExpression: `SET ${updateExpressionParts.join(', ')}`,
    ExpressionAttributeValues: expressionAttributeValues,
    ReturnValues: 'ALL_NEW',
  };

  const result = await dynamoDB.update(params).promise();
  if (!result.Attributes) {
    throw new Error(`Could not update user with ID ${userID}`);
  }
  return result.Attributes as User;
};
