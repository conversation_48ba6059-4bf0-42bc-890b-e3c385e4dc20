import { OPERATION_COSTS } from './constants';

// Account type enum
export type AccountType = 'parent' | 'child';

export interface AuthUrls {
  signup: string;
  signin: string;
  verify: string;
  reset: string;
  update: string;
  resend: string;
  successful: string;
}

export interface User {
  userID: string;
  username: string;
  email: string;
  password?: string; // Optional for Google-based accounts
  emailVerified: boolean;
  accountStatus: 'active' | 'inactive' | 'locked' | 'pending' | 'suspended';
  createdAt: number;
  verificationToken?: string;
  verificationTokenExpiry?: number;
  lastVerificationTokenSentAt?: number;
  publicKey: string;
  loginAttempts?: number;
  lastLoginAttempt?: number;
  lockedUntil?: number;
  resetPasswordOTP?: string;
  resetPasswordOTPExpiry?: number;
  lastResetPasswordRequestAt?: number;
  parentAccount: string;
  organizationName?: string;
  organizationUrl?: string;
  authUrls?: AuthUrls;
  organizationUpdateCount?: number;
  accountType: AccountType;
  emailConfirmationCount?: number;
  resendEmailCount?: number;
  resetPasswordRequestCount?: number;
  passwordUpdateCount?: number;
  signInCount?: number;
  organizationDetailsRetrievalCount?: number;
  childAccountsListRetrievalCount?: number;
  accountBalance?: number;
  availableBalance?: number;
  lastLowBalanceNotificationAt?: number;
  lastCriticalBalanceNotificationAt?: number;
  lastDepletedBalanceNotificationAt?: number;
  jwtSecret?: string; // Only for parent accounts
  lastPasswordChanged?: number;
  domainRestrictionEnabled?: boolean;
  googleId?: string;
  authProvider: 'email' | 'google';
  lastLoginProvider?: 'email' | 'google';
  updatedAt?: number;
  lastLogin?: number;
  secretKey?: string;
  iv?: string;
  googleSsoConfig?: {
    enabled: boolean;
    clientId: string;
    clientSecret: string; // This will be the ENCRYPTED secret
  };
  // Organization ID approach fields
  isPromotedAccount?: boolean;     // Flag to identify accounts promoted from child to parent
  promotedAt?: number;             // Timestamp when promotion occurred
  organizationId?: string;         // Organization that promoted this account
}

export interface CreateUserInput {
  username: string;
  email: string;
  password: string;
  verificationToken?: string;
  verificationTokenExpiry?: number;
  emailVerified?: boolean;
  accountStatus?: string;
  createdAt?: number;
  updatedAt?: number;
  publicKey?: string; // Renamed from PUBLICKey
  lastVerificationTokenSentAt?: number;
  resetPasswordOTPExpiry?: number;
  lastResetPasswordRequestAt?: number;
  parentAccount?: string | null;
  organizationName?: string;
  organizationUrl?: string;
  authUrls?: AuthUrls;
  organizationUpdateCount?: number;
  accountType?: AccountType;
  emailConfirmationCount?: number;
  resendEmailCount?: number;
  resetPasswordRequestCount?: number;
  passwordUpdateCount?: number;
  accountBalance?: number;
  availableBalance?: number;
  lastLowBalanceNotificationAt?: number;
  lastCriticalBalanceNotificationAt?: number;
  lastDepletedBalanceNotificationAt?: number;
  lastPasswordChanged?: number;
  domainRestrictionEnabled?: boolean; // Controls whether widget is restricted to specific domains
  authProvider?: 'email' | 'google';
  lastLoginProvider?: 'email' | 'google';
}

// New interface for parent-child specific operations

export interface UserProfileResponse {
  id: string;
  username: string;
  email: string;
  isEmailVerified: boolean;
  organizationName?: string;
  organizationUrl?: string;
  authUrls?: AuthUrls;
  organizationUpdateCount: number;
  accountType: AccountType;
  parentAccount?: string;
  publicKey?: string; // Add publicKey to response
}

export interface ChildAccountResponse {
  username: string;
  email: string;
  lastSeen: string | null;
  registered: string;
}

export type OperationKey = keyof typeof OPERATION_COSTS;

export interface OperationCounts {
  [key: string]: number | undefined;
  emailConfirmation: number;
  resendConfirmation: number;
  resetPassword: number;
  updatePassword: number;
  childAccounts: number;
  signIn: number;
  organizationUpdate: number;
  organizationDetailsRetrieval: number;
  childAccountsListRetrieval: number;
  lastResetDate?: number;
}

export interface CostBreakdown {
  reads: { [key in OperationKey]: number };
  writes: { [key in OperationKey]: number };
  totalCost: number;
}

export interface ChildAccountsResponse {
  accounts: ChildAccountResponse[];
  lastEvaluatedKey?: any;
  totalAccounts: number;
  activeAccounts: number;
  operationCounts: OperationCounts;
}

// Add after OperationCounts interface
export interface ParentAccountBalance {
  accountBalance: number;    // Total funds in the account
  availableBalance: number;  // Available funds after current charges
}
